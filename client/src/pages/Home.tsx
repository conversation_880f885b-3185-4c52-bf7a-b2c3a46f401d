import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import Sidebar from "@/components/Sidebar";
import MainContent from "@/components/MainContent";
import TopWalletButton from "@/components/TopWalletButton";
import { useChatStore } from "@/store/chatStore";
import { useNebulaSession } from "@/hooks/use-nebula-session";
import { useActiveAccount } from "thirdweb/react";

const Home = () => {
  const { activeChat, setActiveChat, setChats } = useChatStore();
  const { sessionId, isCreatingSession } = useNebulaSession();

  // Check if user is signed in (has an active account)
  const account = useActiveAccount();
  const isSignedIn = !!account?.address;

  // Fetch chat history
  const { data: chats, isLoading: isLoadingChats } = useQuery({
    queryKey: ["/api/chats"],
    staleTime: 60000, // 1 minute
  });

  // Set chats in store when data is loaded
  useEffect(() => {
    if (chats) {
      setChats(chats);

      // Set first chat as active if no active chat
      if (chats.length > 0 && !activeChat) {
        setActiveChat(chats[0]);
      }
    }
  }, [chats, activeChat, setChats, setActiveChat]);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Top Navigation Bar */}
      <header className="fixed top-0 right-0 z-50 p-4">
        <TopWalletButton />
      </header>

      {/* Main Layout */}
      <div className="flex flex-col md:flex-row min-h-screen">
        {/* Only show sidebar if user is signed in */}
        {isSignedIn && <Sidebar isLoading={isLoadingChats} />}
        <MainContent />
      </div>
    </div>
  );
};

export default Home;
