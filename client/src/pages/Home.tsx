import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import Sidebar from "@/components/Sidebar";
import MainContent from "@/components/MainContent";
import TopWalletButton from "@/components/TopWalletButton";
import { useChatStore } from "@/store/chatStore";
import { useNebulaSession } from "@/hooks/use-nebula-session";
import { useActiveAccount } from "thirdweb/react";
import { Button } from "@/components/ui/button";
import { CubeIcon } from "@/components/icons";
import { Sun, Moon } from "lucide-react";

const Home = () => {
  const { activeChat, setActiveChat, setChats } = useChatStore();
  const { sessionId, isCreatingSession } = useNebulaSession();

  // Check if user is signed in (has an active account)
  const account = useActiveAccount();
  const isSignedIn = !!account?.address;

  // Theme state (placeholder for future implementation)
  const [isDarkMode, setIsDarkMode] = useState(true);

  // Theme toggle handler (placeholder for future implementation)
  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    // TODO: Implement actual theme switching logic
  };

  // Fetch chat history
  const { data: chats, isLoading: isLoadingChats } = useQuery({
    queryKey: ["/api/chats"],
    staleTime: 60000, // 1 minute
  });

  // Set chats in store when data is loaded
  useEffect(() => {
    if (chats) {
      setChats(chats);

      // Set first chat as active if no active chat
      if (chats.length > 0 && !activeChat) {
        setActiveChat(chats[0]);
      }
    }
  }, [chats, activeChat, setChats, setActiveChat]);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Conditional Header */}
      {isSignedIn ? (
        /* Signed-in users: Show only wallet button in top right */
        <header className="fixed top-0 right-0 z-50 p-4">
          <TopWalletButton />
        </header>
      ) : (
        /* Non-signed-in users: Show full header with logo and controls */
        <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border/40">
          <div className="flex items-center justify-between px-6 py-4">
            {/* Left side: Web3AI Logo */}
            <div className="flex items-center gap-3">
              <div className="nebula-icon-bg w-8 h-8 flex items-center justify-center">
                <CubeIcon className="text-foreground text-lg" />
              </div>
              <h1 className="text-xl font-bold text-foreground no-underline tracking-wide">
                Web3AI
              </h1>
            </div>

            {/* Right side: Theme toggle and Sign-in button */}
            <div className="flex items-center gap-3">
              {/* Theme toggle button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleTheme}
                className="h-9 w-9 p-0"
                title={
                  isDarkMode ? "Switch to light mode" : "Switch to dark mode"
                }
              >
                {isDarkMode ? (
                  <Sun className="h-4 w-4" />
                ) : (
                  <Moon className="h-4 w-4" />
                )}
              </Button>

              {/* Sign-in button */}
              <TopWalletButton />
            </div>
          </div>
        </header>
      )}

      {/* Main Layout */}
      <div
        className={`flex flex-col md:flex-row min-h-screen ${
          !isSignedIn ? "pt-20" : ""
        }`}
      >
        {/* Only show sidebar if user is signed in */}
        {isSignedIn && <Sidebar isLoading={isLoadingChats} />}
        <MainContent />
      </div>
    </div>
  );
};

export default Home;
