import {
  ArrowRightIcon,
  HelpCircleIcon,
  CodeIcon,
  DollarSignIcon,
  SearchIcon,
  SendIcon,
} from "lucide-react";

interface SuggestionChipsProps {
  onChipClick: (suggestion: string) => void;
}

const suggestions = [
  "What can Nebula do?",
  "Launch a Token",
  "Buy USDC",
  "Analyze the Uniswap contracts",
  "Send ETH to someone",
];

const SuggestionChips = ({ onChipClick }: SuggestionChipsProps) => {
  return (
    <div className="flex flex-wrap justify-center gap-2 mb-4">
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          className="nebula-suggestion-chip"
          onClick={() => onChipClick(suggestion)}
        >
          {suggestion}
          <ArrowRightIcon className="h-3 w-3 text-primary ml-1" />
        </button>
      ))}
    </div>
  );
};

export default SuggestionChips;
